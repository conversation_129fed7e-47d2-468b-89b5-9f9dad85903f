<?xml version="1.0" encoding="UTF-8"?>
<component LANGUAGE_LEVEL="JDK_1_8" inheritJdk="true">
	<output-test url="file://$MODULE_DIR$/target/test-classes"/>
	<contentEntry url="file://$MODULE_DIR$">
		<testFolder url="file://$MODULE_DIR$/Test"/>
		<excludeFolder url="file://$MODULE_DIR$/target"/>
	</contentEntry>
	<lib name="Maven: junit:junit:4.12" scope="TEST"/>
	<lib name="Maven: org.hamcrest:hamcrest-core:1.3" scope="TEST"/>
	<lib name="Maven: org.jacoco:org.jacoco.agent:0.7.6.201602180812" scope="TEST"/>
	<levels>
		<level name="3d" value="project"/>
		<level name="Maven: junit:junit:4.12" value="project"/>
		<level name="Maven: org.hamcrest:hamcrest-core:1.3" value="project"/>
		<level name="Maven: org.ow2.asm:asm-all:5.0.4" value="project"/>
		<level name="Maven: org.jacoco:org.jacoco.agent:0.7.6.201602180812" value="project"/>
	</levels>
</component>
