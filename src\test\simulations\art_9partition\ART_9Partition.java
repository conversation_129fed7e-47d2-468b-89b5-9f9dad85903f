package test.simulations.art_9partition;

import java.util.ArrayList;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import test.ART;
/**
 * Michael's
 * 先写二维的吧
 * */
public class ART_9Partition extends ART {

	private ArrayList<NRectRegion> availableRegions=new ArrayList<>();
	private ArrayList<NRectRegion> testedRegions=new ArrayList<>();
	private ArrayList<NPoint> tests=new ArrayList<>();
	private int index;
	private int round=1;
	private int[] shunxu={4,9,2,7,6,1,8,3,5};
	public ART_9Partition(double[] min, double[] max, Random random, FailurePattern failurePattern) {
		super(min, max, random, failurePattern);
		//availableRegions
		splitRegions(new NRectRegion(new NPoint(min), new NPoint(max)));
	}

	public void splitRegions(NRectRegion region){
		//二维特殊化
		for(int i=0;i<3;i++){
			for(int j=0;j<3;j++){
				
			}
		}
	}
	
	@Override
	public NPoint generateNextTC() {

		NPoint p=new NPoint();
		int indexTemp=9;
		while(indexTemp<(index+1)){
			round++;
			indexTemp+=Math.pow(9, round);
		}
		//需要 round个数来表示
		int shang=(int) ((index-Math.pow(9,(round-1))/9));
		int yushu=(int) ((index-Math.pow(9,(round-1))%9));
		
		//三元组
		//(round,shang,yushu)
		
		NRectRegion region=availableRegions.get(shunxu[index]);
		p=randomCreator.randomPoint(region);
		tests.add(p);
		index++;
		
		
		
		return null;
	}
	public static void main(String[] args) {
		/*int round=1;
		int index=9;
		int indexTemp=9;
		while(indexTemp<(index+1)){
			round++;
			indexTemp+=Math.pow(9, round);
		}
		System.out.println(round);*/
		
		_9Region regions[]=new _9Region[9];
		int count=0;
		int round=0;
		while(count<100){
			
		}
	}
}
